"use client"


import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Eye,
  ChevronLeft,
  ChevronRight,
  Search,
  Filter,
  Package,
  Plus,
  RotateCcw,
  Clock,
  CheckCircle,
  XCircle,
} from "lucide-react";
import { formatPriceRON } from "@/lib/utils";
import { useState, useTransition, useCallback } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Return, ReturnFilters, ReturnStatistics, OrderItemForReturn } from "@/types/returns";
import { formatDate } from "@/lib/order-utils";
import { ReturnStatus } from "@/generated/prisma";
import ReturnDetailsDialog from "./ReturnDetailsDialog";
import NewReturnDialog from "./NewReturnDialog";
import ReturnStatusBadge from "./ReturnStatusBadge";



interface ReturnsPageProps {
  initialReturns: Return[];
  pagination: {
    total: number;
    pages: number;
    currentPage: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  filters: ReturnFilters;
  statistics: ReturnStatistics;
}

export default function ReturnsPage({ 
  initialReturns, 
  pagination, 
  filters, 
  statistics 
}: ReturnsPageProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isPending, startTransition] = useTransition();
  const [selectedReturn, setSelectedReturn] = useState<Return | null>(null);
  const [showNewReturnDialog, setShowNewReturnDialog] = useState(false);
  const [orderItemsForReturn] = useState<OrderItemForReturn[]>([]);

  // Filter state
  const [searchTerm, setSearchTerm] = useState(filters.search || "");
  const [statusFilter, setStatusFilter] = useState<ReturnFilters['status']>(filters.status || "all");

  const updateFilters = useCallback((newFilters: Partial<ReturnFilters>) => {
    const params = new URLSearchParams(searchParams);
    
    Object.entries(newFilters).forEach(([key, value]) => {
      if (value && value !== "" && value !== "all") {
        params.set(key, value.toString());
      } else {
        params.delete(key);
      }
    });

    // Reset to page 1 when filters change
    if (newFilters.search !== undefined || newFilters.status !== undefined) {
      params.delete('page');
    }

    startTransition(() => {
      router.push(`/account/returns?${params.toString()}`);
    });
  }, [router, searchParams]);

  const handleSearch = useCallback(() => {
    updateFilters({ search: searchTerm });
  }, [searchTerm, updateFilters]);

  const handleStatusChange = useCallback((status: string) => {
    const typedStatus = status as ReturnFilters['status'];
    setStatusFilter(typedStatus);
    updateFilters({ status: typedStatus });
  }, [updateFilters]);

  const handlePageChange = useCallback((page: number) => {
    updateFilters({ page });
  }, [updateFilters]);

  const getStatusBadge = (status: ReturnStatus) => {
    return <ReturnStatusBadge status={status} />;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Returnări</h1>
          <p className="text-muted-foreground">
            Gestionează returnările tale și urmărește statusul acestora
          </p>
        </div>
        <Button
          className="flex items-center gap-2"
          onClick={() => setShowNewReturnDialog(true)}
        >
          <Plus className="h-4 w-4" />
          Returnare nouă
        </Button>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statistics.total}</div>
            <p className="text-xs text-muted-foreground">
              Returnări în total
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">În așteptare</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statistics.pending}</div>
            <p className="text-xs text-muted-foreground">
              Returnări în procesare
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Finalizate</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statistics.completed}</div>
            <p className="text-xs text-muted-foreground">
              Returnări finalizate
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Respinse</CardTitle>
            <XCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statistics.rejected}</div>
            <p className="text-xs text-muted-foreground">
              Returnări respinse
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Filtrează returnările</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  placeholder="Caută după numărul returnării sau comenzii..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <Select value={statusFilter} onValueChange={handleStatusChange}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Toate statusurile</SelectItem>
                  <SelectItem value="requested">Solicitată</SelectItem>
                  <SelectItem value="approved">Aprobată</SelectItem>
                  <SelectItem value="rejected">Respinsă</SelectItem>
                  <SelectItem value="awaitingReceipt">În așteptare</SelectItem>
                  <SelectItem value="received">Primită</SelectItem>
                  <SelectItem value="inspected">Verificată</SelectItem>
                  <SelectItem value="refundIssued">Rambursată</SelectItem>
                  <SelectItem value="completed">Finalizată</SelectItem>
                  <SelectItem value="cancelled">Anulată</SelectItem>
                </SelectContent>
              </Select>
              <Button onClick={handleSearch} disabled={isPending}>
                <Filter className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Returns List */}
      <Card>
        <CardHeader>
          <CardTitle>Returnările tale</CardTitle>
          <CardDescription>
            {pagination.total > 0 
              ? `Afișez ${initialReturns.length} din ${pagination.total} returnări`
              : "Nu ai încă returnări"
            }
          </CardDescription>
        </CardHeader>
        <CardContent>
          {initialReturns.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-12 space-y-4">
              <RotateCcw className="h-12 w-12 text-muted-foreground" />
              <div className="text-center space-y-2">
                <h3 className="text-lg font-semibold">Nu ai returnări</h3>
                <p className="text-muted-foreground">
                  Când vei face o returnare, aceasta va apărea aici.
                </p>
              </div>
              <Button
                className="flex items-center gap-2"
                onClick={() => setShowNewReturnDialog(true)}
              >
                <Plus className="h-4 w-4" />
                Creează prima returnare
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {initialReturns.map((returnItem) => (
                <div
                  key={returnItem.id}
                  className="border rounded-lg p-4 hover:bg-muted/50 transition-colors"
                >
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <h3 className="font-semibold">{returnItem.returnNumber}</h3>
                        {getStatusBadge(returnItem.status)}
                      </div>
                      <div className="text-sm text-muted-foreground space-y-1">
                        <p>Comandă: {returnItem.order.orderNumber}</p>
                        <p>Data: {formatDate(returnItem.createdAt)}</p>
                        <p>Produse: {returnItem.returnItems.length}</p>
                        {returnItem.refundAmount && (
                          <p>Sumă rambursată: {formatPriceRON(returnItem.refundAmount)}</p>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setSelectedReturn(returnItem)}
                        className="flex items-center gap-2"
                      >
                        <Eye className="h-4 w-4" />
                        Detalii
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Pagination */}
      {pagination.pages > 1 && (
        <div className="flex items-center justify-between">
          <p className="text-sm text-muted-foreground">
            Pagina {pagination.currentPage} din {pagination.pages}
          </p>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(pagination.currentPage - 1)}
              disabled={!pagination.hasPrev || isPending}
            >
              <ChevronLeft className="h-4 w-4" />
              Anterior
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(pagination.currentPage + 1)}
              disabled={!pagination.hasNext || isPending}
            >
              Următor
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}

      {/* Return Details Dialog */}
      <ReturnDetailsDialog
        returnData={selectedReturn}
        open={!!selectedReturn}
        onOpenChange={(open) => !open && setSelectedReturn(null)}
        onReturnUpdated={() => {
          // Refresh the page to get updated data
          router.refresh();
        }}
      />

      {/* New Return Dialog */}
      <NewReturnDialog
        open={showNewReturnDialog}
        onOpenChange={setShowNewReturnDialog}
        orderItems={orderItemsForReturn}
        onReturnCreated={() => {
          // Refresh the page to get updated data
          router.refresh();
        }}
      />
    </div>
  );
}
