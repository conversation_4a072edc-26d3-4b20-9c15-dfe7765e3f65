"use client"

import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import {
  Clock,
  XCircle,
  AlertCircle,
  Package,
} from "lucide-react";
import { Return } from "@/types/returns";
import { formatDate } from "@/lib/order-utils";
import { formatPriceRON } from "@/lib/utils";
import { generateReturnTimeline } from "@/app/getData/returns";
import { ReturnStatus } from "@/generated/prisma";

import { cancelReturn } from "@/app/actions/returns";
import { useState, useTransition } from "react";
import { toast } from "sonner";
import ReturnStatusBadge from "./ReturnStatusBadge";
import ReturnTimeline from "./ReturnTimeline";
import ReturnItemCard from "./ReturnItemCard";

// Return reason labels
const RETURN_REASON_LABELS = {
  wrongItem: "Produs greșit",
  defective: "Produs defect",
  damaged: "Produs deteriorat",
  notAsDescribed: "Nu corespunde descrierii",
  noLongerWanted: "Nu mai doresc produsul",
  other: "Altul",
};

interface ReturnDetailsDialogProps {
  returnData: Return | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onReturnUpdated?: () => void;
}

export default function ReturnDetailsDialog({
  returnData,
  open,
  onOpenChange,
  onReturnUpdated,
}: ReturnDetailsDialogProps) {
  const [isPending, startTransition] = useTransition();
  const [isLoading, setIsLoading] = useState(false);

  if (!returnData) return null;

  const timeline = generateReturnTimeline(returnData);
  const canCancel = returnData.status === 'requested';

  const handleCancelReturn = async () => {
    if (!returnData || !canCancel) return;

    setIsLoading(true);
    startTransition(async () => {
      try {
        const result = await cancelReturn(returnData.id);
        
        if (result.success) {
          toast.success("Returnarea a fost anulată cu succes");
          onReturnUpdated?.();
          onOpenChange(false);
        } else {
          toast.error(result.error || "A apărut o eroare la anularea returnării");
        }
      } catch {
        toast.error("A apărut o eroare neașteptată");
      } finally {
        setIsLoading(false);
      }
    });
  };

  const getStatusBadge = (status: ReturnStatus) => {
    return <ReturnStatusBadge status={status} />;
  };



  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <div>
              <DialogTitle className="text-xl">
                Returnare {returnData.returnNumber}
              </DialogTitle>
              <DialogDescription>
                Comandă {returnData.order.orderNumber} • {formatDate(returnData.createdAt)}
              </DialogDescription>
            </div>
            {getStatusBadge(returnData.status)}
          </div>
        </DialogHeader>

        <div className="space-y-6">
          {/* Return Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <h3 className="font-semibold flex items-center gap-2">
                <AlertCircle className="h-4 w-4" />
                Informații returnare
              </h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Motiv:</span>
                  <span>{RETURN_REASON_LABELS[returnData.reason]}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Data solicitării:</span>
                  <span>{formatDate(returnData.createdAt)}</span>
                </div>
                {returnData.approvedAt && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Data aprobării:</span>
                    <span>{formatDate(returnData.approvedAt)}</span>
                  </div>
                )}
                {returnData.refundAmount && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Sumă rambursată:</span>
                    <span className="font-semibold">{formatPriceRON(returnData.refundAmount)}</span>
                  </div>
                )}
              </div>
              {returnData.additionalNotes && (
                <div className="space-y-2">
                  <span className="text-sm font-medium">Note adiționale:</span>
                  <p className="text-sm text-muted-foreground bg-muted p-3 rounded-md">
                    {returnData.additionalNotes}
                  </p>
                </div>
              )}
            </div>

            <div className="space-y-4">
              <h3 className="font-semibold flex items-center gap-2">
                <Package className="h-4 w-4" />
                Comandă originală
              </h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Număr comandă:</span>
                  <span>{returnData.order.orderNumber}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Data comenzii:</span>
                  <span>{formatDate(returnData.order.placedAt)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Produse returnate:</span>
                  <span>{returnData.returnItems.length}</span>
                </div>
              </div>
            </div>
          </div>

          <Separator />

          {/* Return Items */}
          <div className="space-y-4">
            <h3 className="font-semibold">Produse returnate</h3>
            <div className="space-y-3">
              {returnData.returnItems.map((item) => (
                <ReturnItemCard
                  key={item.id}
                  item={item}
                  showInspectionDetails={true}
                />
              ))}
            </div>
          </div>

          <Separator />

          {/* Timeline */}
          <div className="space-y-4">
            <h3 className="font-semibold flex items-center gap-2">
              <Clock className="h-4 w-4" />
              Cronologia returnării
            </h3>
            <ReturnTimeline events={timeline} />
          </div>

          {/* Actions */}
          <div className="flex justify-end gap-2 pt-4 border-t">
            {canCancel && (
              <Button
                variant="destructive"
                onClick={handleCancelReturn}
                disabled={isLoading || isPending}
                className="flex items-center gap-2"
              >
                <XCircle className="h-4 w-4" />
                {isLoading ? "Se anulează..." : "Anulează returnarea"}
              </Button>
            )}
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Închide
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
