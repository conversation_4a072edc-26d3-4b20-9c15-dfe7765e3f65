import { OrderStatus, ReturnStatus, ReturnReason, ReturnItemReason } from '@/generated/prisma';
import { z } from 'zod'; // For input validation

export const FamilyCodeSchema = z.object({
    familyCode: z.
        string().
        min(4, { message: 'Family code must be at least 4 characters long' }).
        max(10, { message: 'Family code must be at most 10 characters long' })
});


//export const userIdStringSchema = z.string().min(24);
export const productCodSchema = z.string().length(11)
export const cuidSchema =   z.string().cuid()
export const userIdClerkSchema =  z.string().startsWith('user_')

export const addObservationsToCartSchema = z.string()

export const updateCartSchema = z.object({
    itemId: cuidSchema.optional(),
    vinNotes: z.string().optional(),
    addVinNotesToInvoice: z.boolean().optional(),
    addToOrder: z.boolean().optional(),
    quantity: z.number().optional(),
    orderNotes: z.string().optional(),
});


export const categoryIdSchemaMSSQL = z.string().min(7).max(20) // Alphanumeric with dashes only
//export const categoryIdSchemaMSSQL = z.string().min(1).max(20).regex(/^[A-Z0-9-]+$/i) // Alphanumeric with dashes only


export const passwordChangeSchema = z.object({
  currentPassword: z.string()
    .min(1, { message: 'Parola curentă este obligatorie' }),

  newPassword: z.string()
    .min(8, { message: 'Parola nouă trebuie să aibă cel puțin 8 caractere' })
    .max(128, { message: 'Parola nouă nu poate avea mai mult de 128 de caractere' })
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/, {
      message: 'Parola nouă trebuie să conțină cel puțin o literă mică, o literă mare, o cifră și un caracter special'
    }),

  confirmPassword: z.string()
    .min(1, { message: 'Confirmarea parolei este obligatorie' }),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: 'Parolele nu se potrivesc',
  path: ['confirmPassword'],
});

export const notificationPreferencesSchema = z.object({
  emailNotifications: z.boolean(),
  pushNotifications: z.boolean(),
  smsNotifications: z.boolean(),
  newsletterOptIn: z.boolean(),
});
export type NotificationPreferencesInput = z.infer<typeof notificationPreferencesSchema>;

export const securitySettingsSchema = z.object({
  twoFactorEnabled: z.boolean(),
});

export type PasswordChangeInput = z.infer<typeof passwordChangeSchema>;

export type SecuritySettingsInput = z.infer<typeof securitySettingsSchema>;

export const orderFiltersSchema = z.object({
  // Use z.coerce to automatically convert string -> number
  page: z.coerce.number().int().min(1).default(1),
  limit: z.coerce.number().int().min(5).max(50).default(10),
  
  // This union handles a string that is either 'all' or a valid OrderStatus
  status: z.union([
    z.nativeEnum(OrderStatus),
    z.literal('all')
  ]).default('all'), // If status is missing or invalid, it defaults to 'all'

  // Use optional() for optional fields
  search: z.string().optional().default(''),
  dateFrom: z.string().optional().default(''),
  dateTo: z.string().optional().default(''),
});

export const orderNumberSchema = z.string().startsWith('ORD-').min(13).max(17)


export const deleteAccountSchema = z.object({
  reason: z.string().min(1, 'Motivul este obligatoriu').max(500, 'Motivul este prea lung'),
  confirmationText: z.literal('STERGE CONTUL MEU', {
    errorMap: () => ({ message: 'Textul de confirmare nu este corect' })
  })
});

export const revokeSessionSchema = z.object({
  sessionId: z.string().min(10, 'Session ID is required').max(45, 'Session ID is too long'),
});

export const suspendAccountSchema = z.object({
  reason: z.string().min(3, 'Motivul este prea scurt').max(100, 'Motivul este prea lung'),
});

export type SuspendAccountInput = z.infer<typeof suspendAccountSchema>;
export type DeleteAccountInput = z.infer<typeof deleteAccountSchema>;


export const clerkProfileSchema = z.object({
  firstName: z.string().min(1, "Prenumele este obligatoriu"),
  lastName: z.string().min(1, "Numele de familie este obligatoriu"),
  email: z.string().email("Email invalid"),
  phoneNumber: z.string()
    .optional()
    .refine((val) => {
      if (!val || val.trim() === '') return true; // Allow empty
      // E.164 format: +[country code][subscriber number]
      const e164Regex = /^\+[1-9]\d{1,14}$/;
      return e164Regex.test(val);
    }, {
      message: "Numărul de telefon trebuie să fie în format internațional (ex: +***********)"
    }),
});

export type ClerkProfileInput = z.infer<typeof clerkProfileSchema>;

export const emailVerificationSchema = z.object({
  code: z.string().min(6, "Codul de verificare trebuie să aibă 6 caractere"),
});

export type EmailVerificationInput = z.infer<typeof emailVerificationSchema>;


const phoneNumberSchema = z.object({
  backup_codes: z.array(z.string()).nullable().optional(), // Added .nullable()
  created_at: z.number().transform(val => val ? new Date(val) : null),
  default_second_factor: z.boolean().optional(),
  id: z.string().optional(),
  linked_to: z.array(
    z.object({
      id: z.string(),
      type: z.string().regex(/^oauth_[a-z]+$/),
    })
  ).optional(),
  object: z.literal('phone_number'),
  phone_number: z.string(),
  reserved: z.boolean(),
  reserved_for_second_factor: z.boolean().optional(),
  updated_at: z.number().transform(val => val ? new Date(val) : null),
  verification: z.object({
    attempts: z.number(),
    channel: z.literal('sms'),
    expire_at: z.number().transform(val => val ? new Date(val) : null),
    status: z.enum(['unverified', 'verified', 'failed', 'expired']),
    strategy: z.enum(['phone_code', 'email_code', 'reset_password_email_code']),
  }).nullable().optional(), // Added .nullable() in case verification can be null
}).passthrough();

const emailAddressSchema = z.object({
  email_address: z.string().email(),
  id: z.string(),
  linked_to: z.array(z.object({
    id: z.string(),
    type: z.string().regex(/^oauth_[a-z]+$/)
  })),
  object: z.literal('email_address'),
  verification: z.object({
    status: z.enum(['verified', 'unverified']),
    strategy: z.string(), // Can be 'ticket', 'email_code', etc.
  }).nullable(),
  // Optional fields that might be present
  created_at: z.number().optional(),
  updated_at: z.number().optional(),
  reserved: z.boolean().optional(),
  matches_sso_connection: z.boolean().optional(),
}).passthrough(); // Allow additional fields

const clerkUserCreatedOrUpdatedSchema = z
  .object({
    // Core User Attributes
    id: z.string(),
    email_addresses: z.array(emailAddressSchema).min(1),
    first_name: z.string().nullable().optional(),
    last_name: z.string().nullable().optional(),
    image_url: z.string().url().optional(), 
    has_image: z.boolean().optional(),
    primary_email_address_id: z.string().nullable().optional(),
    primary_phone_number_id: z.string().nullable().optional(),
    phone_numbers: z.array(phoneNumberSchema).nullable().optional(),

    
    // security
    password_enabled: z.boolean().optional(),
    two_factor_enabled: z.boolean().optional(),
    mfa_enabled_at: z.number().nullable().optional().transform(val => val ? new Date(val) : null),
    mfa_disabled_at: z.number().nullable().optional().transform(val => val ? new Date(val) : null),
    totp_enabled: z.boolean().optional(),

    //lock out 
    banned: z.boolean().optional(),
    locked: z.boolean().optional(),
    lockout_expires_in_seconds: z.number().nullable().optional(),
    verification_attempts_remaining: z.number().nullable().optional(),

    //enabled from the dashboard
    delete_self_enabled: z.boolean().optional(),
    backup_code_enabled: z.boolean().optional(),

    //legal
    legal_accepted_at : z.number().nullable().optional().transform(val => val ? new Date(val) : null),

    created_at: z.number(),
    updated_at: z.number(),
    last_sign_in_at: z.number().nullable().optional(),
  })
  // Use .passthrough() to allow other fields Clerk might send
  // This prevents future webhooks from failing if Clerk adds a new field
  .passthrough();

const clerkUserDeletedSchema = z.object({
  id: z.string(),
  object: z.string().optional(),
  deleted: z.boolean(),
}).passthrough();

// The final webhook schema, which you will parse against
export const webhookSchema = z.discriminatedUnion('type', [
  z.object({
    type: z.enum(['user.created', 'user.updated']),
    data: clerkUserCreatedOrUpdatedSchema,
    object: z.literal('event'),
  }),
  z.object({
    type: z.literal('user.deleted'),
    data: clerkUserDeletedSchema,
    object: z.literal('event'),
  }),
]);

// Return validation schemas
export const returnShippingAddressSchema = z.object({
  fullName: z.string()
    .min(2, { message: 'Numele complet trebuie să aibă cel puțin 2 caractere' })
    .max(100, { message: 'Numele complet nu poate avea mai mult de 100 de caractere' }),

  address: z.string()
    .min(5, { message: 'Adresa trebuie să aibă cel puțin 5 caractere' })
    .max(200, { message: 'Adresa nu poate avea mai mult de 200 de caractere' }),

  city: z.string()
    .min(2, { message: 'Orașul trebuie să aibă cel puțin 2 caractere' })
    .max(50, { message: 'Orașul nu poate avea mai mult de 50 de caractere' }),

  county: z.string()
    .min(2, { message: 'Județul trebuie să aibă cel puțin 2 caractere' })
    .max(50, { message: 'Județul nu poate avea mai mult de 50 de caractere' }),

  phoneNumber: z.string()
    .min(10, { message: 'Numărul de telefon trebuie să aibă cel puțin 10 cifre' })
    .max(15, { message: 'Numărul de telefon nu poate avea mai mult de 15 cifre' })
    .regex(/^[0-9+\-\s()]+$/, { message: 'Numărul de telefon conține caractere invalide' }),

  notes: z.string()
    .max(500, { message: 'Notele nu pot avea mai mult de 500 de caractere' })
    .optional(),
});

export const returnItemSchema = z.object({
  orderItemId: cuidSchema,
  quantity: z.number()
    .min(1, { message: 'Cantitatea trebuie să fie cel puțin 1' })
    .max(100, { message: 'Cantitatea nu poate fi mai mare de 100' }),
  reason: z.nativeEnum(ReturnItemReason, {
    errorMap: () => ({ message: 'Motivul returnării este invalid' })
  }),
  description: z.string()
    .max(1000, { message: 'Descrierea nu poate avea mai mult de 1000 de caractere' })
    .optional(),
});

export const createReturnSchema = z.object({
  orderId: cuidSchema,
  reason: z.nativeEnum(ReturnReason, {
    errorMap: () => ({ message: 'Motivul returnării este invalid' })
  }),
  additionalNotes: z.string()
    .max(2000, { message: 'Notele adiționale nu pot avea mai mult de 2000 de caractere' })
    .optional(),
  items: z.array(returnItemSchema)
    .min(1, { message: 'Trebuie să selectați cel puțin un produs pentru returnare' })
    .max(50, { message: 'Nu puteți returna mai mult de 50 de produse odată' }),
  shippingAddress: returnShippingAddressSchema,
});

export const returnFiltersSchema = z.object({
  status: z.union([
    z.nativeEnum(ReturnStatus),
    z.literal('all')
  ]).optional().default('all'),
  search: z.string().max(100).optional().default(''),
  page: z.coerce.number().min(1).optional().default(1),
  limit: z.coerce.number().min(1).max(50).optional().default(10),
  dateFrom: z.string().optional().default(''),
  dateTo: z.string().optional().default(''),
});

export const updateReturnStatusSchema = z.object({
  returnId: cuidSchema,
  status: z.nativeEnum(ReturnStatus),
  notes: z.string()
    .max(1000, { message: 'Notele nu pot avea mai mult de 1000 de caractere' })
    .optional(),
});

// Type exports for the schemas
export type ReturnShippingAddressInput = z.infer<typeof returnShippingAddressSchema>;
export type ReturnItemInput = z.infer<typeof returnItemSchema>;
export type CreateReturnInput = z.infer<typeof createReturnSchema>;
export type ReturnFiltersInput = z.infer<typeof returnFiltersSchema>;
export type UpdateReturnStatusInput = z.infer<typeof updateReturnStatusSchema>;